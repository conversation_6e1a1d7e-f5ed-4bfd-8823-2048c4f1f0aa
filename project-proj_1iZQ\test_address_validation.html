<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址驗證測試</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-2xl font-bold mb-6 text-center text-gray-800">地址驗證功能測試</h1>
        <div id="test-form"></div>
    </div>

    <!-- 載入必要的工具函數 -->
    <script src="utils/cityDistricts.js"></script>
    <script src="utils/orderUtils.js"></script>
    <script src="utils/sheetsUtils.js"></script>

    <script type="text/babel">
        // 簡化版的 OrderForm 組件，專門測試地址驗證功能
        function AddressValidationTest() {
            const [formData, setFormData] = React.useState({
                deliveryMethod: '宅配到府',
                district: '台北市',
                area: '中正區',
                address: ''
            });
            
            const [addressWarning, setAddressWarning] = React.useState('');
            const [districts, setDistricts] = React.useState([]);

            React.useEffect(() => {
                if (formData.district) {
                    setDistricts(cityDistricts[formData.district] || []);
                }
            }, [formData.district]);

            // 地址驗證處理函數
            const handleAddressChange = (e) => {
                const address = e.target.value;
                setFormData({...formData, address});
                
                // 即時檢查地址格式
                if (address.trim() && formData.deliveryMethod === '宅配到府') {
                    const hasNumber = /\d+號/.test(address);
                    if (!hasNumber && address.length > 3) {
                        setAddressWarning('⚠️ 建議地址包含門牌號碼（例如：XX路XX號）');
                    } else {
                        setAddressWarning('');
                    }
                } else {
                    setAddressWarning('');
                }
            };

            const handleDeliveryMethodChange = (e) => {
                const method = e.target.value;
                setFormData({...formData, deliveryMethod: method});
                setAddressWarning('');
            };

            const validateAddress = () => {
                if (formData.deliveryMethod === '宅配到府') {
                    if (!formData.district || !formData.area || !formData.address) {
                        alert('❌ 宅配到府請填寫完整地址資訊');
                        return false;
                    }
                    
                    // 檢查地址是否包含門牌號碼
                    const addressText = formData.address.trim();
                    const hasNumber = /\d+號/.test(addressText);
                    
                    if (!hasNumber) {
                        alert('❌ 地址格式不完整\n\n請確認地址包含門牌號碼（例如：XX路XX號）\n這有助於配送員準確找到您的地址');
                        return false;
                    }
                }
                
                alert('✅ 地址驗證通過！');
                return true;
            };

            return (
                <div className="space-y-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-bold mb-2">測試說明</h3>
                        <ul className="text-sm space-y-1">
                            <li>• 選擇「宅配到府」配送方式</li>
                            <li>• 在地址欄位輸入不同格式的地址</li>
                            <li>• 觀察即時警示訊息</li>
                            <li>• 點擊「驗證地址」測試最終驗證</li>
                        </ul>
                    </div>

                    <div className="space-y-4">
                        <div>
                            <label className="block text-gray-700 mb-2 font-medium">配送方式</label>
                            <select
                                className="w-full px-3 py-2 border rounded-lg"
                                value={formData.deliveryMethod}
                                onChange={handleDeliveryMethodChange}
                            >
                                <option value="宅配到府">宅配到府</option>
                                <option value="超商取貨">超商取貨</option>
                            </select>
                        </div>

                        {formData.deliveryMethod === '宅配到府' && (
                            <>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2 font-medium">縣市</label>
                                        <select
                                            className="w-full px-3 py-2 border rounded-lg"
                                            value={formData.district}
                                            onChange={(e) => setFormData({...formData, district: e.target.value, area: ''})}
                                        >
                                            {Object.keys(cityDistricts).map(city => (
                                                <option key={city} value={city}>{city}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2 font-medium">地區</label>
                                        <select
                                            className="w-full px-3 py-2 border rounded-lg"
                                            value={formData.area}
                                            onChange={(e) => setFormData({...formData, area: e.target.value})}
                                        >
                                            <option value="">請選擇地區</option>
                                            {districts.map(area => (
                                                <option key={area} value={area}>{area}</option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                                
                                <div>
                                    <label className="block text-gray-700 mb-2 font-medium">地址</label>
                                    <input
                                        type="text"
                                        className={`w-full px-3 py-2 border rounded-lg ${addressWarning ? 'border-orange-400 bg-orange-50' : ''}`}
                                        placeholder="請輸入完整地址（例如：中正路123號）"
                                        value={formData.address}
                                        onChange={handleAddressChange}
                                    />
                                    {addressWarning && (
                                        <p className="text-orange-600 text-sm mt-2 flex items-center">
                                            <span className="mr-2">⚠️</span>
                                            {addressWarning}
                                        </p>
                                    )}
                                </div>
                            </>
                        )}

                        <button
                            onClick={validateAddress}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors"
                        >
                            驗證地址
                        </button>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-bold mb-2">測試案例</h4>
                        <div className="text-sm space-y-1">
                            <p><strong>✅ 正確格式：</strong></p>
                            <p className="ml-4">• 中正路123號</p>
                            <p className="ml-4">• 民生東路456號2樓</p>
                            <p className="ml-4">• 忠孝西路1段100號</p>
                            <p><strong>❌ 錯誤格式：</strong></p>
                            <p className="ml-4">• 中正路（缺少門牌號碼）</p>
                            <p className="ml-4">• 民生東路附近（不明確）</p>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<AddressValidationTest />, document.getElementById('test-form'));
    </script>
</body>
</html>
