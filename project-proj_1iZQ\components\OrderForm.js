function OrderForm() {
    try {
        const [formData, setFormData] = React.useState({
            products: {
                radish: 0,
                taro: 0,
                hongkong: 0
            },
            shipping: 0,
            totalAmount: 0,
            customerName: '',
            phone: '',
            deliveryMethod: '',
            district: '',
            area: '',
            address: '',
            preferredDate: '',
            preferredTime: '上午 (13點前)',
            contactMethod: '',
            socialAccount: '',
            paymentMethod: '',
            notes: '',
            storeName: '',
            storeAddress: '',
            storeId: ''
        });

        const [showBankInfo, setShowBankInfo] = React.useState(false);
        const [districts, setDistricts] = React.useState([]);
        const [showDeliveryAlert, setShowDeliveryAlert] = React.useState(false);
        const [deliveryAlertType, setDeliveryAlertType] = React.useState('');
        const [showStorePicker, setShowStorePicker] = React.useState(false);
        const [showOrderConfirm, setShowOrderConfirm] = React.useState(false);
        const [addressWarning, setAddressWarning] = React.useState('');

        React.useEffect(() => {
            calculateTotal();
            if (formData.district) {
                setDistricts(cityDistricts[formData.district] || []);
            }
        }, [formData.products, formData.district]);

        // 可選擇的到貨日期狀態
        const [availableDates, setAvailableDates] = React.useState([]);
        const [datesLoading, setDatesLoading] = React.useState(false);

        // 從後台 API 獲取可選擇的到貨日期
        const fetchAvailableDates = async (deliveryMethod) => {
            try {
                setDatesLoading(true);
                const response = await fetch(`./api/delivery_settings.php?action=available_dates&delivery_method=${encodeURIComponent(deliveryMethod)}`);
                const result = await response.json();

                if (result.success) {
                    setAvailableDates(result.data);
                } else {
                    console.error('獲取可選日期失敗:', result.message);
                    setAvailableDates([]);
                }
            } catch (error) {
                console.error('獲取可選日期錯誤:', error);
                setAvailableDates([]);
            } finally {
                setDatesLoading(false);
            }
        };

        // 初始化時載入預設日期（如果沒有選擇配送方式）
        React.useEffect(() => {
            // 頁面載入時，如果已有配送方式則載入對應日期
            if (formData.deliveryMethod) {
                fetchAvailableDates(formData.deliveryMethod);
            }
        }, []); // 只在組件掛載時執行一次

        // 產品數量控制函數
        const updateProductQuantity = (product, change) => {
            const currentQty = parseInt(formData.products[product]) || 0;
            const newQty = Math.max(0, currentQty + change);
            setFormData({
                ...formData,
                products: { ...formData.products, [product]: newQty }
            });
        };

        const calculateTotal = () => {
            const radishTotal = (parseInt(formData.products.radish) || 0) * 250;
            const taroTotal = (parseInt(formData.products.taro) || 0) * 350;
            const hongkongTotal = (parseInt(formData.products.hongkong) || 0) * 350;
            const subtotal = radishTotal + taroTotal + hongkongTotal;

            let shipping = 0;
            if (subtotal > 0 && subtotal < 350) {
                shipping = 100;
            }

            setFormData(prev => ({
                ...prev,
                shipping,
                totalAmount: subtotal + shipping
            }));
        };

        const handleDeliveryMethodChange = (e) => {
            const method = e.target.value;
            setFormData({...formData, deliveryMethod: method, storeName: '', storeAddress: '', storeId: '', preferredDate: ''});

            // 清除地址警示訊息
            setAddressWarning('');

            if (method === '宅配到府') {
                setDeliveryAlertType('home');
                setShowDeliveryAlert(true);
            } else if (method === '超商取貨') {
                setDeliveryAlertType('store');
                setShowDeliveryAlert(true);
            }

            // 根據配送方式獲取可選日期
            if (method) {
                fetchAvailableDates(method);
            }
        };

        // 門市選擇相關函數
        const openStorePicker = () => {
            setShowStorePicker(true);
        };

        const closeStorePicker = () => {
            setShowStorePicker(false);
        };

        const setStoreInfo = (name, address, id) => {
            setFormData(prev => ({
                ...prev,
                storeName: name,
                storeAddress: address,
                storeId: id
            }));
        };

        // 全域函數設定（供iframe使用）
        React.useEffect(() => {
            window.setStoreInfo = setStoreInfo;
            window.closeStorePicker = closeStorePicker;
        }, []);

        const handlePaymentMethodChange = (e) => {
            const method = e.target.value;
            setFormData({...formData, paymentMethod: method});
            setShowBankInfo(method === '銀行轉帳');
        };

        // 地址驗證處理函數
        const handleAddressChange = (e) => {
            const address = e.target.value;
            setFormData({...formData, address});

            // 即時檢查地址格式
            if (address.trim() && formData.deliveryMethod === '宅配到府') {
                const hasNumber = /\d+號/.test(address);
                if (!hasNumber && address.length > 3) {
                    setAddressWarning('⚠️地址須包含門牌號碼');
                } else {
                    setAddressWarning('');
                }
            } else {
                setAddressWarning('');
            }
        };

        const [isSubmitting, setIsSubmitting] = React.useState(false);

        const handleSubmit = async (e) => {
            e.preventDefault();

            if (isSubmitting) {
                return; // 防止重複提交
            }

            // 基本驗證
            if (!validateOrderData()) {
                return;
            }

            // 顯示訂單確認彈窗
            setShowOrderConfirm(true);
        };

        const confirmSubmitOrder = async () => {
            setIsSubmitting(true);
            setShowOrderConfirm(false);

            try {
                // 顯示提交中狀態
                const submitButton = document.querySelector('button[type="submit"]');
                if (submitButton) {
                    const originalText = submitButton.textContent;
                    submitButton.textContent = '⏳ 處理中...';
                    submitButton.disabled = true;
                }

                // 提交訂單到 Google Sheets
                const result = await submitOrderToSheets(formData);

                if (result.success) {
                    // 顯示成功訊息
                    const detailedMessage = showDetailedResults(result);
                    alert(detailedMessage);

                    // 重新載入頁面
                    window.location.reload();
                } else {
                    // 顯示錯誤訊息
                    const errorDetails = getErrorDetails(result);
                    alert(errorDetails);
                }

            } catch (error) {
                console.error('訂單提交系統錯誤：', error);
                alert('❌ 系統發生未預期的錯誤，請稍後再試。\n\n錯誤詳情：' + error.message);
            } finally {
                setIsSubmitting(false);

                // 恢復按鈕狀態
                const submitButton = document.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.textContent = '確認送出訂單';
                    submitButton.disabled = false;
                }
            }
        };

        const validateOrderData = () => {
            // 檢查必填欄位
            const requiredFields = [
                { field: 'customerName', name: '姓名' },
                { field: 'phone', name: '電話' },
                { field: 'deliveryMethod', name: '配送方式' },
                { field: 'preferredDate', name: '希望到貨日期' },
                { field: 'contactMethod', name: '聯繫方式' },
                { field: 'socialAccount', name: '社群帳號' },
                { field: 'paymentMethod', name: '付款方式' }
            ];

            for (const { field, name } of requiredFields) {
                if (!formData[field] || formData[field].trim() === '') {
                    alert(`❌ 請填寫${name}`);
                    return false;
                }
            }

            // 檢查配送方式相關欄位
            if (formData.deliveryMethod === '宅配到府') {
                if (!formData.district || !formData.area || !formData.address) {
                    alert('❌ 宅配到府請填寫完整地址資訊');
                    return false;
                }

                // 檢查地址是否包含門牌號碼
                const addressText = formData.address.trim();
                const hasNumber = /\d+號/.test(addressText);

                if (!hasNumber) {
                    alert('❌ 地址格式不完整，請確認地址包含門牌號碼');
                    return false;
                }
            } else if (formData.deliveryMethod === '超商取貨') {
                if (!formData.storeName || !formData.storeAddress) {
                    alert('❌ 超商取貨請選擇取貨門市');
                    return false;
                }
            }

            // 檢查產品數量
            const totalQuantity = (parseInt(formData.products.radish) || 0) +
                                (parseInt(formData.products.taro) || 0) +
                                (parseInt(formData.products.hongkong) || 0);

            if (totalQuantity <= 0) {
                alert('❌ 請至少選擇一項商品');
                return false;
            }

            // 檢查電話號碼格式
            const phoneRegex = /^[0-9]{8,10}$/;
            const cleanPhone = formData.phone.replace(/\D/g, '');
            if (!phoneRegex.test(cleanPhone)) {
                alert('❌ 電話號碼格式不正確，請輸入8-10位數字');
                return false;
            }

            return true;
        };

        return (
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 space-y-4" data-name="order-form">
                <h3 className="text-xl font-bold mb-4">訂購表單</h3>

                <div className="space-y-4" data-name="product-selection">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                        <span className="text-gray-700 font-medium flex-1">原味蘿蔔糕 (NT$ 250/條)</span>
                        <div className="flex items-center space-x-2 flex-shrink-0">
                            <button
                                type="button"
                                onClick={() => updateProductQuantity('radish', -1)}
                                className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold transition-colors"
                            >
                                -
                            </button>
                            <span className="w-6 text-center font-semibold text-sm">{formData.products.radish}</span>
                            <button
                                type="button"
                                onClick={() => updateProductQuantity('radish', 1)}
                                className="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold transition-colors"
                            >
                                +
                            </button>
                        </div>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                        <span className="text-gray-700 font-medium flex-1">芋頭粿 (NT$ 350/條)</span>
                        <div className="flex items-center space-x-2 flex-shrink-0">
                            <button
                                type="button"
                                onClick={() => updateProductQuantity('taro', -1)}
                                className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold transition-colors"
                            >
                                -
                            </button>
                            <span className="w-6 text-center font-semibold text-sm">{formData.products.taro}</span>
                            <button
                                type="button"
                                onClick={() => updateProductQuantity('taro', 1)}
                                className="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold transition-colors"
                            >
                                +
                            </button>
                        </div>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                        <span className="text-gray-700 font-medium flex-1">港式蘿蔔糕 (NT$ 350/條)</span>
                        <div className="flex items-center space-x-2 flex-shrink-0">
                            <button
                                type="button"
                                onClick={() => updateProductQuantity('hongkong', -1)}
                                className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold transition-colors"
                            >
                                -
                            </button>
                            <span className="w-6 text-center font-semibold text-sm">{formData.products.hongkong}</span>
                            <button
                                type="button"
                                onClick={() => updateProductQuantity('hongkong', 1)}
                                className="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold transition-colors"
                            >
                                +
                            </button>
                        </div>
                    </div>
                </div>

                <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-xl shadow-lg" data-name="order-summary">
                    <h4 className="font-bold mb-2">訂單摘要</h4>
                    <div className="grid grid-cols-2 gap-2">
                        <p>商品小計：</p>
                        <p className="text-right">NT$ {formData.totalAmount - formData.shipping}</p>
                        <p>運費：</p>
                        <p className="text-right">NT$ {formData.shipping}</p>
                        <p className="font-bold">總計：</p>
                        <p className="text-right font-bold" style={{fontSize: '18px', color: '#dc2626'}}>NT$ {formData.totalAmount}</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4" data-name="customer-info" style={{fontSize: '16px'}}>
                    <div>
                        <label className="block text-gray-700 mb-2">姓名</label>
                        <input
                            type="text"
                            className="w-full px-2 py-0.5 border rounded text-sm"
                            required
                            value={formData.customerName}
                            onChange={(e) => setFormData({...formData, customerName: e.target.value})}
                        />
                    </div>
                    <div>
                        <label className="block text-gray-700 mb-2">電話</label>
                        <input
                            type="tel"
                            className="w-full px-2 py-0.5 border rounded text-sm"
                            required
                            value={formData.phone}
                            onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        />
                    </div>
                    <div>
                        <label className="block text-gray-700 mb-2">配送方式</label>
                        <select
                            className="w-full px-2 py-1.5 border rounded text-sm"
                            required
                            value={formData.deliveryMethod}
                            onChange={handleDeliveryMethodChange}
                        >
                            <option value="">請選擇配送方式</option>
                            <option value="宅配到府">宅配到府</option>
                            <option value="超商取貨">超商取貨 (7-11)</option>
                        </select>
                    </div>

                    {/* 宅配到府地址選擇 */}
                    {formData.deliveryMethod === '宅配到府' && (
                        <>
                            <div className="grid grid-cols-2 gap-2">
                                <div>
                                    <label className="block text-gray-700 mb-2">縣市</label>
                                    <select
                                        className="w-full px-2 py-1.5 border rounded text-sm"
                                        required
                                        value={formData.district}
                                        onChange={(e) => setFormData({...formData, district: e.target.value, area: ''})}
                                    >
                                        {Object.keys(cityDistricts).map(city => (
                                            <option key={city} value={city}>{city}</option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-gray-700 mb-2">地區</label>
                                    <select
                                        className="w-full px-2 py-1.5 border rounded text-sm"
                                        required
                                        value={formData.area}
                                        onChange={(e) => setFormData({...formData, area: e.target.value})}
                                    >
                                        <option value="">請選擇地區</option>
                                        {districts.map(area => (
                                            <option key={area} value={area}>{area}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-gray-700 mb-2">地址</label>
                                <input
                                    type="text"
                                    className={`w-full px-2 py-1.5 border rounded text-sm ${addressWarning ? 'border-orange-400' : ''}`}
                                    required
                                    placeholder="請輸入完整地址（例如：中正路123號）"
                                    value={formData.address}
                                    onChange={handleAddressChange}
                                />
                                {addressWarning && (
                                    <p className="text-orange-600 text-xs mt-1 flex items-center">
                                        <span className="mr-1">⚠️</span>
                                        {addressWarning}
                                    </p>
                                )}
                            </div>
                        </>
                    )}

                    {/* 超商取貨門市選擇 */}
                    {formData.deliveryMethod === '超商取貨' && (
                        <div className="md:col-span-2">
                            <div className="flex justify-between items-center mb-2">
                                <label className="block text-gray-700">選擇取貨門市</label>
                                <button
                                    type="button"
                                    onClick={openStorePicker}
                                    className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded text-xs transition-colors"
                                >
                                    選擇7-11門市
                                </button>
                            </div>
                            <div className="border rounded p-3 bg-gray-50">
                                {formData.storeName ? (
                                    <div>
                                        <p className="font-semibold text-green-600" style={{fontSize: '16px'}}>✓ 已選擇門市</p>
                                        <p style={{fontSize: '16px'}}>{formData.storeName} 門市</p>
                                        <p className="text-gray-600" style={{fontSize: '14px'}}>{formData.storeAddress}</p>
                                        <p className="text-gray-600" style={{fontSize: '14px'}}>店號：{formData.storeId}</p>
                                    </div>
                                ) : (
                                    <p className="text-gray-500">尚未選擇門市</p>
                                )}
                            </div>
                        </div>
                    )}
                    <div>
                        <label className="block text-gray-700 mb-2">希望到貨日期</label>
                        <select
                            className="w-full px-2 py-1.5 border rounded text-sm"
                            required
                            value={formData.preferredDate}
                            onChange={(e) => setFormData({...formData, preferredDate: e.target.value})}
                            disabled={datesLoading || !formData.deliveryMethod}
                        >
                            <option value="">
                                {datesLoading ? '載入中...' :
                                 !formData.deliveryMethod ? '請先選擇配送方式' :
                                 '請選擇到貨日期'}
                            </option>
                            {availableDates.map(date => (
                                <option key={date.value} value={date.value}>{date.display}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-gray-700 mb-2">希望到貨時間</label>
                        <select
                            className="w-full px-2 py-1.5 border rounded text-sm"
                            required
                            value={formData.preferredTime}
                            onChange={(e) => setFormData({...formData, preferredTime: e.target.value})}
                        >
                            <option value="上午">上午 (13點前)</option>
                            <option value="下午">下午 (13-18點)</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-gray-700 mb-2">透過什麼聯繫賣家</label>
                        <select
                            className="w-full px-2 py-1.5 border rounded text-sm"
                            required
                            value={formData.contactMethod}
                            onChange={(e) => setFormData({...formData, contactMethod: e.target.value})}
                        >
                            <option value="">請選擇聯繫方式</option>
                            <option value="facebook">Facebook</option>
                            <option value="line">Line</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-gray-700 mb-2">社群帳號名稱</label>
                        <input
                            type="text"
                            className="w-full px-2 py-0.5 border rounded text-sm"
                            required
                            placeholder="請輸入您的FB/Line名稱"
                            value={formData.socialAccount}
                            onChange={(e) => setFormData({...formData, socialAccount: e.target.value})}
                        />
                    </div>
                    <div>
                        <label className="block text-gray-700 mb-2">付款方式</label>
                        <select
                            className="w-full px-2 py-1.5 border rounded text-sm"
                            required
                            value={formData.paymentMethod}
                            onChange={handlePaymentMethodChange}
                        >
                            <option value="">請選擇付款方式</option>
                            <option value="銀行轉帳">銀行轉帳</option>
                            <option value="貨到付款">貨到付款</option>
                        </select>
                    </div>
                    {showBankInfo && (
                        <div className="md:col-span-2 p-3 sm:p-4 bg-blue-50 rounded">
                            <h4 className="font-bold mb-1 sm:mb-2">銀行帳戶資訊</h4>
                            {/*<p>戶名：XXX</p>*/}
                            <p className="leading-tight sm:leading-normal">銀行：中國信託 (代碼：822)</p>
                            <p className="leading-tight sm:leading-normal">帳號：************</p>
                        </div>
                    )}
                    <div className="md:col-span-2">
                        <label className="block text-gray-700 mb-2">備註</label>
                        <textarea
                            className="w-full px-2 py-1.5 border rounded text-sm"
                            rows="3"
                            value={formData.notes}
                            onChange={(e) => setFormData({...formData, notes: e.target.value})}
                        ></textarea>
                    </div>
                </div>

                <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full px-6 py-3 text-white rounded-md transition-colors ${
                        isSubmitting
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-red-600 hover:bg-red-700'
                    }`}
                    data-name="submit-button"
                >
                    {isSubmitting ? '⏳ 處理中...' : '確認送出訂單'}
                </button>

                {isSubmitting && (
                    <div className="text-center text-sm mt-2">
                        <div className="flex items-center justify-center space-x-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                            <span className="text-red-600 font-medium">提交中...</span>
                        </div>
                    </div>
                )}

                {/* 配送方式提醒彈窗 */}
                {showDeliveryAlert && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 max-w-md mx-4">
                            <h3 className="text-lg font-bold mb-4">
                                {deliveryAlertType === 'home' ? '宅配到府注意事項' : '超商取貨注意事項'}
                            </h3>
                            <div className="mb-4">
                                {deliveryAlertType === 'home' ? (
                                    <div className="space-y-1 sm:space-y-2">
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">🏠</span>
                                            <p className="text-sm leading-tight sm:leading-normal">請務必填寫正確的收件地址和電話</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">⏰</span>
                                            <p className="text-sm leading-tight sm:leading-normal">配送時間可能會有1-2小時的誤差</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">📦</span>
                                            <p className="text-sm leading-tight sm:leading-normal">若到貨時無法收貨，可請宅配人員改約時間或寄放附近7-11門市取貨</p>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-1 sm:space-y-2">
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">📱</span>
                                            <p className="text-sm leading-tight sm:leading-normal">包裹到超商會有手機簡訊通知，商品只保留5天請儘速取貨</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">❄️</span>
                                            <p className="text-sm leading-tight sm:leading-normal">產品只能冷藏不能冷凍，領貨時若是冷凍請馬上聯絡我們或拒收</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">📦</span>
                                            <p className="text-sm leading-tight sm:leading-normal">出貨後2日內未收到超商到貨簡訊，請主動致電門市詢問</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <button
                                onClick={() => setShowDeliveryAlert(false)}
                                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded transition-colors"
                            >
                                我知道了
                            </button>
                        </div>
                    </div>
                )}

                {/* 門市選擇器彈窗 */}
                {showStorePicker && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg w-full max-w-4xl mx-4 h-5/6 flex flex-col">
                            <div className="flex justify-between items-center p-4 border-b">
                                <h3 className="text-lg font-bold">選擇7-11門市</h3>
                                <button
                                    onClick={closeStorePicker}
                                    className="text-gray-500 hover:text-gray-700 text-2xl"
                                >
                                    ×
                                </button>
                            </div>
                            <div className="flex-1 overflow-hidden">
                                <iframe
                                    src="store-selector.php"
                                    className="w-full h-full border-0"
                                    title="門市選擇器"
                                ></iframe>
                            </div>
                        </div>
                    </div>
                )}

                {/* 訂單確認彈窗 */}
                {showOrderConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                        <div className="bg-white rounded-lg p-6 w-full max-w-none mx-auto max-h-5/6 overflow-y-auto" style={{width: window.innerWidth >= 768 ? '500px' : '90vw'}}>
                            <h3 className="text-lg font-bold mb-4">確認訂單內容</h3>
                            <div className="space-y-3" style={{fontSize: '15px'}}>
                                <div className="border-b pb-2">
                                    <h4 className="font-semibold mb-2">訂購商品</h4>
                                    {formData.products.radish > 0 && (
                                        <p>原味蘿蔔糕：{formData.products.radish} 條 (NT$ {formData.products.radish * 250})</p>
                                    )}
                                    {formData.products.taro > 0 && (
                                        <p>芋頭粿：{formData.products.taro} 條 (NT$ {formData.products.taro * 350})</p>
                                    )}
                                    {formData.products.hongkong > 0 && (
                                        <p>港式蘿蔔糕：{formData.products.hongkong} 條 (NT$ {formData.products.hongkong * 350})</p>
                                    )}
                                    <p className="font-semibold mt-2">運費：NT$ {formData.shipping}</p>
                                    <p className="font-bold text-lg" style={{color: '#dc2626'}}>總計：NT$ {formData.totalAmount}</p>
                                </div>
                                <div className="border-b pb-2">
                                    <h4 className="font-semibold mb-2">客戶資訊</h4>
                                    <p>姓名：{formData.customerName}</p>
                                    <p>電話：{formData.phone}</p>
                                    <p>聯繫方式：{formData.contactMethod}</p>
                                    <p>社群帳號：{formData.socialAccount}</p>
                                </div>
                                <div className="border-b pb-2">
                                    <h4 className="font-semibold mb-2">配送資訊</h4>
                                    <p>配送方式：{formData.deliveryMethod === '超商取貨' ? '門市取貨(7-11)' : formData.deliveryMethod}</p>
                                    {formData.deliveryMethod === '宅配到府' ? (
                                        <p>地址：{formData.district} {formData.area} {formData.address}</p>
                                    ) : (
                                        <div>
                                            <p>取貨門市：{formData.storeName} 門市</p>
                                            <p>門市地址：{formData.storeAddress}</p>
                                            <p>店號：{formData.storeId}</p>
                                        </div>
                                    )}
                                    <p>希望到貨日期：{formData.preferredDate}</p>
                                    <p>希望到貨時間：{formData.preferredTime}</p>
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-2">付款方式</h4>
                                    <p>{formData.paymentMethod}</p>
                                    {formData.notes && (
                                        <div className="mt-2">
                                            <p className="font-semibold">備註：</p>
                                            <p>{formData.notes}</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="flex space-x-3 mt-6">
                                <button
                                    onClick={() => setShowOrderConfirm(false)}
                                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded transition-colors"
                                >
                                    返回修改
                                </button>
                                <button
                                    onClick={confirmSubmitOrder}
                                    className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 rounded transition-colors"
                                >
                                    確認提交
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </form>
        );
    } catch (error) {
        console.error('OrderForm component error:', error);
        reportError(error);
        return null;
    }
}
