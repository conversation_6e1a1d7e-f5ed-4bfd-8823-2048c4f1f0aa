{"version": 3, "sources": ["../../@radix-ui/react-slot/src/Slot.tsx", "../../@radix-ui/react-compose-refs/src/composeRefs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAQvB,SAAS,OAAU,KAAqB,OAAU;AAChD,MAAI,OAAO,QAAQ,YAAY;AAC7B,QAAI,KAAK;EACX,WAAW,QAAQ,QAAQ,QAAQ,QAAW;AAC3C,QAAkC,UAAU;EAC/C;AACF;AAMA,SAAS,eAAkB,MAAwB;AACjD,SAAO,CAAC,SAAY,KAAK,QAAQ,CAAC,QAAQ,OAAO,KAAK,IAAI,CAAC;AAC7D;AAMA,SAAS,mBAAsB,MAAwB;AAErD,SAAa,kBAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD;;;ADGM,yBAAA;AAvBN,IAAM,OAAa,kBAAmC,CAAC,OAAO,iBAAiB;AAC7E,QAAM,EAAE,UAAU,GAAG,UAAU,IAAI;AACnC,QAAM,gBAAsB,gBAAS,QAAQ,QAAQ;AACrD,QAAM,YAAY,cAAc,KAAK,WAAW;AAEhD,MAAI,WAAW;AAEb,UAAM,aAAa,UAAU,MAAM;AAEnC,UAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,UAAI,UAAU,WAAW;AAGvB,YAAU,gBAAS,MAAM,UAAU,IAAI,EAAG,QAAa,gBAAS,KAAK,IAAI;AACzE,eAAa,sBAAe,UAAU,IACjC,WAAW,MAAM,WAClB;MACN,OAAO;AACL,eAAO;MACT;IACF,CAAC;AAED,eACE,wBAAC,WAAA,EAAW,GAAG,WAAW,KAAK,cAC5B,UAAM,sBAAe,UAAU,IACtB,oBAAa,YAAY,QAAW,WAAW,IACrD,KAAA,CACN;EAEJ;AAEA,aACE,wBAAC,WAAA,EAAW,GAAG,WAAW,KAAK,cAC5B,SAAA,CACH;AAEJ,CAAC;AAED,KAAK,cAAc;AAUnB,IAAM,YAAkB,kBAAgC,CAAC,OAAO,iBAAiB;AAC/E,QAAM,EAAE,UAAU,GAAG,UAAU,IAAI;AAEnC,MAAU,sBAAe,QAAQ,GAAG;AAClC,UAAM,cAAc,cAAc,QAAQ;AAC1C,WAAa,oBAAa,UAAU;MAClC,GAAG,WAAW,WAAW,SAAS,KAAK;;MAEvC,KAAK,eAAe,YAAY,cAAc,WAAW,IAAI;IAC/D,CAAC;EACH;AAEA,SAAa,gBAAS,MAAM,QAAQ,IAAI,IAAU,gBAAS,KAAK,IAAI,IAAI;AAC1E,CAAC;AAED,UAAU,cAAc;AAMxB,IAAM,YAAY,CAAC,EAAE,SAAS,MAAqC;AACjE,aAAO,wBAAA,6BAAA,EAAG,SAAA,CAAS;AACrB;AAMA,SAAS,YAAY,OAAqD;AACxE,SAAa,sBAAe,KAAK,KAAK,MAAM,SAAS;AACvD;AAEA,SAAS,WAAW,WAAqB,YAAsB;AAE7D,QAAM,gBAAgB,EAAE,GAAG,WAAW;AAEtC,aAAW,YAAY,YAAY;AACjC,UAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAM,iBAAiB,WAAW,QAAQ;AAE1C,UAAM,YAAY,WAAW,KAAK,QAAQ;AAC1C,QAAI,WAAW;AAEb,UAAI,iBAAiB,gBAAgB;AACnC,sBAAc,QAAQ,IAAI,IAAI,SAAoB;AAChD,yBAAe,GAAG,IAAI;AACtB,wBAAc,GAAG,IAAI;QACvB;MACF,WAES,eAAe;AACtB,sBAAc,QAAQ,IAAI;MAC5B;IACF,WAES,aAAa,SAAS;AAC7B,oBAAc,QAAQ,IAAI,EAAE,GAAG,eAAe,GAAG,eAAe;IAClE,WAAW,aAAa,aAAa;AACnC,oBAAc,QAAQ,IAAI,CAAC,eAAe,cAAc,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;IACpF;EACF;AAEA,SAAO,EAAE,GAAG,WAAW,GAAG,cAAc;AAC1C;AAOA,SAAS,cAAc,SAA6B;;AAElD,MAAI,UAAS,YAAO,yBAAyB,QAAQ,OAAO,KAAK,MAApD,mBAAuD;AACpE,MAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,MAAI,SAAS;AACX,WAAQ,QAAgB;EAC1B;AAGA,YAAS,YAAO,yBAAyB,SAAS,KAAK,MAA9C,mBAAiD;AAC1D,YAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,MAAI,SAAS;AACX,WAAO,QAAQ,MAAM;EACvB;AAGA,SAAO,QAAQ,MAAM,OAAQ,QAAgB;AAC/C;AAEA,IAAM,OAAO;", "names": ["React"]}