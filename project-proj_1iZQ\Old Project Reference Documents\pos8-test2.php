<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<style>
:root {
  --primary-color: #d35400;
  --secondary-color: #e67e22;
  --accent-color: #27ae60;
  --text-color: #2c3e50;
  --light-bg: #fff5eb;
  --card-bg: #fff;
  --highlight: #e74c3c;
  --border-radius: 12px;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), #2ecc71);
}

body {
    margin: 0;
    padding: 3px;
    width: 100%;
    height: auto;
    min-height: 100vh;
    font-family: 'Noto Sans TC', "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
    background: linear-gradient(135deg, var(--light-bg) 0%, #f8f0e3 50%, var(--light-bg) 100%);
    display: flex;
    flex-direction: column;
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
    overscroll-behavior-y: contain;
    box-sizing: border-box;
}

	.center-image {
  width: 100%;
  max-width: 100%;
  height: auto;
  object-fit: contain;
  }

	h2 {
			text-align: center;
			margin: 0 auto;
            color: var(--primary-color);
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

	#selectedItem1,
	#selectedItem2,
	#selectedItem3 {
  font-size: 20px;
  font-weight: bold;
  color: var(--accent-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.05));
  border-radius: 8px;
  border-left: 4px solid var(--accent-color);
  margin-bottom: 8px;
  display: block;
		  }

    .form-section, .form-section2, .form-section-upper, .form-section-lower {
  width: 100%;
  max-width: 450px;
  box-sizing: border-box;
  margin: 5px auto;
  padding: 5px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  }

.form-section {
  width: 95%;
  margin: 10px auto;
  background: linear-gradient(135deg, var(--card-bg) 0%, #fafafa 100%);
  border: 2px solid var(--secondary-color);
  transition: var(--transition);
  }

	.form-section2 {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: transparent;
  }

	  .form-section-upper {
  background: linear-gradient(135deg, #fff 0%, var(--light-bg) 50%, #fff 100%);
  border: 2px solid var(--primary-color);
  position: relative;
  overflow: hidden;
	}

	  .form-section-lower {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid var(--accent-color);
  position: relative;
  overflow: hidden;
	}

    label {
		display: block;
		margin-bottom: 2px;
  font-size: 16px;
  font-weight: bold;
  color: var(--text-color);
		}

	select,
	input,
	textarea {
		width: 100%;
  box-sizing: border-box;
		padding: 8px;
		margin-bottom: 8px;
  font-size: 16px;
  font-weight: bold;
  border: 2px solid var(--secondary-color);
  background-color: var(--card-bg);
  border-radius: 8px;
  transition: var(--transition);
  }

  select:focus,
  input:focus,
  textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(211, 84, 0, 0.1);
    transform: translateY(-1px);
  }

	#addressSection {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-start;
}

.address-selects {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.address-input {
  display: flex;
  width: 100%;
  gap: 5px;
  align-items: center;
  flex-wrap: wrap;
	}

	#county {
  width: 95px;
  min-width: 95px;
}

#districtSelect {
  width: 95px;
  min-width: 95px;
	}

#additionalInput {
  flex: 1;
}

/* 地址驗證相關樣式 */
.address-validation-info {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 6px;
  padding: 10px;
  margin: 5px 0;
  font-size: 14px;
  color: #1976d2;
}

.address-validation-warning {
  background: #fff3e0;
  border: 1px solid #ff9800;
  border-radius: 6px;
  padding: 10px;
  margin: 5px 0;
  font-size: 14px;
  color: #f57c00;
}

.address-validation-error {
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 6px;
  padding: 10px;
  margin: 5px 0;
  font-size: 14px;
  color: #d32f2f;
}

.address-suggestion {
  background: #f3e5f5;
  border: 1px solid #9c27b0;
  border-radius: 6px;
  padding: 8px;
  margin: 3px 0;
  font-size: 13px;
  color: #7b1fa2;
  cursor: pointer;
  transition: background-color 0.2s;
}

.address-suggestion:hover {
  background: #e1bee7;
}

#addressValidationResult {
  margin-top: 5px;
}

#storeButton {
  width: auto;
  padding: 8px 16px;
  margin: 5px 0 5px 0;
  font-size: 14px;
  font-weight: bold;
  min-width: 100px;
  background: var(--gradient-accent);
  border: 2px solid var(--accent-color);
}

#storeButton.input-error {
  border: 2px solid #ff3333 !important;
  background-color: #fff0f0 !important;
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
  color: #000000 !important;
}

#selectedStore {
  margin-left: 10px;
  font-size: 16px;
  font-weight: bold;
  color: var(--accent-color);
  display: inline-block;
  vertical-align: middle;
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
  border-radius: 8px;
  border: 2px solid var(--accent-color);
  transition: var(--transition);
}

#storeText {
  color: #000000;
  font-size: 20px;
  font-weight: bold;
  white-space: nowrap;
  margin-left: 8px;
}

    button {
  width: 100%;
  padding: 15px;
  margin: 10px 0;
  font-size: 18px;
  font-weight: bold;
  background: var(--gradient-primary);
  color: white;
		  border: none;
		  border-radius: var(--border-radius);
		  cursor: pointer;
  font-family: 'Noto Sans TC', "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
  letter-spacing: 1px;
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
		}

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    button:hover {
      background: linear-gradient(135deg, #b8440e, #c55a1a);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      transform: translateY(-2px);
    }

    button:hover::before {
      left: 100%;
    }

    button:disabled {
      background: linear-gradient(135deg, #cccccc, #999999);
      cursor: not-allowed;
      transform: none;
    }

	.input-error {
  border: 2px solid #ff3333 !important;
  background-color: #fff0f0 !important;
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.shake {
  animation: shake 0.4s ease-in-out;
}

	#totalAmount {
  font-size: 20px;
  font-weight: bold;
  color: var(--highlight);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 768px) {
  body {
    padding: 5px;
  }

  .form-section, .form-section2, .form-section-upper, .form-section-lower {
    width: 95%;
    margin: 5px auto;
    padding: 8px;
  }

  h2 {
    font-size: 1.5em;
    margin: 10px 0;
  }

  label {
    font-size: 18px;
  }

  input, select, textarea {
    font-size: 16px;
  }
}

@supports (-webkit-touch-callout: none) {
  body {
    min-height: -webkit-fill-available;
  }
}

.quantity-group {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 10px;
}

.quantity-group label {
  flex: 0 0 auto;
  margin: 0;
  min-width: 45px;
  font-size: 18px; /* 增加字體大小 */
}

.quantity-group input {
  flex: 1;
  margin: 0;
}

.item-row {
  margin-bottom: 8px;
}

.form-section::before {
  display: block;
  text-align: center;
  color: #8b4513;
  font-size: 24px;
  margin-bottom: 10px;
}

.form-section::after {
  display: block;
  text-align: center;
  color: #8b4513;
  font-size: 24px;
  margin-top: 10px;
  transform: rotate(180deg);
}

#phoneError {
  color: #8b0000;
  font-style: italic;
}

/* 添加門市選擇器的樣式 */
#storePickerOverlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

#storePicker {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    height: 80vh;
    background: white;
    border-radius: 10px;
    z-index: 1001;
    overflow: hidden;
}

#storePickerFrame {
    width: 100%;
    height: 100%;
    border: none;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1002;
    transition: background-color 0.2s;
}

.close-button:hover {
    background: rgba(0, 0, 0, 0.7);
}

.close-button .material-icons {
    font-size: 24px;
}

/* 錯誤提示相關樣式 */
.input-error {
    border: 2px solid #ff3333 !important;
    background-color: #fff0f0 !important;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.4s ease-in-out;
}

/* 模態框通用樣式 */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    max-width: 80%;
    width: 100%;
    max-width: 400px;
}

.modal-content h3 {
    margin-top: 0;
    color: #ff3333;
    font-size: 1.2em;
}

.modal-content ul {
    margin: 10px 0;
    padding-left: 20px;
    color: #666;
}

.modal-button {
    width: 100%;
    padding: 12px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    margin-top: 15px;
    transition: var(--transition);
    font-weight: bold;
}

.modal-button:hover {
    background: linear-gradient(135deg, #b8440e, #c55a1a);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.reminder-overlay, .delivery-reminder-overlay {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 95%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.reminder-container, .delivery-reminder-container {
  background: white;
  padding: 20px;
  border-radius: 10px;
  max-width: 90%;
  max-height: 90%;
  position: relative;
  overflow: hidden;
}

.reminder-image, .delivery-reminder-image {
  width: 100%;
  max-width: 500px;
  height: auto;
  display: block;
  margin: 0 auto;
}

.close-reminder, .close-delivery-reminder {
  display: block;
  width: 100%;
  padding: 12px;
  background: var(--gradient-accent);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  margin-top: 15px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.close-reminder:hover, .close-delivery-reminder:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}
</style>
</head>

<body onload="calculateTotal()">
<form action="sheets_api_handler.php"
      method="post"
      class="form-section2"
      id="orderForm"
      onsubmit="return handleFormSubmit(event)"
      novalidate>
     <!-- 添加隱藏的 IP 輸入欄位 -->
     <input type="hidden" id="userip" name="userip">
      <br>
    <div class="form-section form-section-upper">
      <!-- 上區塊內容 -->
      <div class="form-section">
        <!-- 第一組訂單項目 -->
        <div class="item-row">
          <span id="selectedItem1">原味蘿蔔糕 $250</span>
          <input type="hidden" id="itemSelection1" name="itemSelection1" value="原味蘿蔔糕,250">
          <div class="quantity-group">
        <label for="quantity1">數量:</label>
        <input type="number" id="quantity1" name="quantity1" oninput="calculateTotal()">
          </div>
        </div>

        <!-- 第二組訂單項目 -->
        <div class="item-row">
          <span id="selectedItem2">極濃芋頭糕 $350</span>
        <input type="hidden" id="itemSelection2" name="itemSelection2" value="芋頭粿,350">
          <div class="quantity-group">
        <label for="quantity2">數量:</label>
        <input type="number" id="quantity2" name="quantity2" oninput="calculateTotal()">
          </div>
        </div>

        <!-- 第三組訂單項目 -->
        <div class="item-row">
          <span id="selectedItem3">港式蘿蔔糕 $350</span>
        <input type="hidden" id="itemSelection3" name="itemSelection3" value="港式蘿蔔糕,350">
          <div class="quantity-group">
        <label for="quantity3">數量:</label>
        <input type="number" id="quantity3" name="quantity3" oninput="calculateTotal()">
          </div>
        </div>

        <!-- 運費 -->
        <div class="quantity-group">
          <label for="shippingFee">運費:</label>
          <span id="shippingFee" style="flex: 1; padding: 4px 8px;">$0</span>
        </div>
        <input type="hidden" id="hiddenShippingFee" name="shippingFee">

        <!-- 總金額 -->
        <div class="quantity-group">
          <label for="totalAmount">總金額:</label>
          <span id="totalAmount" style="flex: 1; padding: 4px 8px;">$0</span>
        </div>
        <input type="hidden" id="hiddenTotalAmount" name="totalAmount">
      </div>
    </div>

    <div class="form-section form-section-lower">
      <!-- 下區塊內容 -->
      <div class="form-section">
		<label for="name">姓名：</label>
		<input type="text" id="name" name="name" required>
		<label for="phone">聯絡電話：</label>
		<input type="tel" id="phone" name="phone" placeholder="0XXXXXXXXX" oninput="validatePhoneNumber()" required>
		<span id="phoneError" style="color: red; display: none;">輸入格式錯誤，0XXXXXXXXX</span>
        <!-- 宅配方式 -->
        <label for="storeLocation">配送收貨方式：</label>
		<select id="storeLocation" name="storeLocation" onchange="showAdditionalInput()">
			<option value="" selected disabled>請選擇收貨方式</option>
			<option value="宅配到府">宅配到府</option>
			<option value="7-11門市">7-11門市</option>
		</select>

		<!-- 新增台灣縣市下拉菜單 -->
		<div id="addressSection" style="display: none;">
			<div class="address-selects">
				<select id="county" name="county" required onchange="onCountyChange()">
				<option value="">縣市</option>
				<option value="基隆市">基隆市</option>
				<option value="台北市">台北市</option>
				<option value="新北市">新北市</option>
				<option value="桃園市">桃園市</option>
				<option value="新竹市">新竹市</option>
				<option value="新竹縣">新竹縣</option>
				<option value="苗栗縣">苗栗縣</option>
				<option value="台中市">台中市</option>
				<option value="彰化縣">彰化縣</option>
				<option value="南投縣">南投縣</option>
				<option value="雲林縣">雲林縣</option>
				<option value="嘉義市">嘉義市</option>
				<option value="嘉義縣">嘉義縣</option>
				<option value="台南市">台南市</option>
				<option value="高雄市">高雄市</option>
				<option value="屏東縣">屏東縣</option>
				<option value="台東縣">台東縣</option>
				<option value="花蓮縣">花蓮縣</option>
				<option value="宜蘭縣">宜蘭縣</option>
				<option value="澎湖縣">澎湖縣</option>
				<option value="金門縣">金門縣</option>
				<option value="連江縣">連江縣</option>
			</select>
				<select id="districtSelect" name="district" style="display: none;">
					<option value="">地區</option>
				</select>
			</div>
			<div class="address-input">
			<input type="text" id="additionalInput" name="additionalInput"
					   placeholder="請輸入詳細地址" required oninput="validateAddress()">
				<button type="button" id="storeButton" onclick="openStoreSelector()"
						style="display: none;">
					選擇門市
				</button>
				<span id="selectedStore" style="display: none;"></span>
			</div>
			<div id="addressValidationResult"></div>
		</div>
        <label for="deliveryDate">希望到貨日：</label>
        <select id="deliveryDate" name="deliveryDate" required>
            <option value="">請先選擇配送方式</option>
        </select>
		<!-- 時段 -->
        <label for="deliveryTime">希望到貨時段：</label>
        <select id="deliveryTime" name="deliveryTime">
            <option value="上午">上午 (13點前)</option>
            <option value="下午">下午 (13點~18點)</option>
        </select>
		<label for="facebookline">透過什麼聯繫家：</label>
		<select id="facebookline" name="facebookline" required>
		  <option value="臉書粉絲團">臉書粉絲團</option>
		  <option value="Line官方">Line官方</option>
		</select>
		<input type="text" id="facebooklineid" name="facebooklineid" placeholder="務必留下LINE或臉書名稱以便聯繫" required>
		<label for="pay">付款方式：</label>
		<select id="pay" name="pay" required>
		  <option value="" selected disabled>請選擇付款方式</option>
		  <option value="貨到付款">貨到付款</option>
		  <option value="轉帳匯款">轉帳匯款</option>
		</select>
		<div id="bankInfo" style="display: none; margin-top: 5px; padding: 8px; background: #f5f5f5; border-radius: 5px; font-size: 18px;" onchange="updateIframeHeight()">
		  <div style="margin-bottom: 3px;"><strong>銀行：中國信託&nbsp;&nbsp;&nbsp;代號822</strong></div>
		  <div style="margin-bottom: 3px;"><strong>帳號：************</strong></div>
		</div>
        <label for="notes">備註:</label>
		<textarea id="notes" name="notes"></textarea>

        <button id="submit-button" type="submit">提交訂單</button>
      </div>
    </div>
  </form>

<!-- 驗證提示視窗 -->
<div id="validationOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.3); width: 85%; max-width: 400px;">
        <h3 id="validationTitle" style="margin-top: 0;"></h3>
        <ul id="validationErrors" style="margin: 15px 0; color: #000; font-size: 18px;"></ul>
        <div style="display: flex; justify-content: center;">
            <button onclick="closeValidationPopup()" style="width: 90%; padding: 12px; background: linear-gradient(135deg, #d35400, #e67e22); color: white; border: none; border-radius: 12px; cursor: pointer; font-size: 16px; font-weight: bold; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">確定</button>
        </div>
    </div>
</div>

<!-- 門市選擇器 -->
<div id="storePickerOverlay" class="modal-overlay"></div>
<div id="storePicker" class="modal-window">
    <button class="close-button" onclick="closeStorePicker()">
        <span class="material-icons">close</span>
    </button>
    <iframe id="storePickerFrame" src="about:blank"></iframe>
</div>

<!-- 添加提醒視窗 -->
<div class="reminder-overlay" id="reminderOverlay">
  <div class="reminder-container">
    <img src="/images/IMG_3851.jpg.webp" alt="提醒事項" class="reminder-image">
    <button class="close-reminder" onclick="closeReminder()">關閉提醒</button>
  </div>
</div>

<!-- 添加宅配提醒視窗 -->
<div class="delivery-reminder-overlay" id="deliveryReminderOverlay">
  <div class="delivery-reminder-container">
    <img src="/images/IMG_3852.png.webp" alt="宅配提醒事項" class="delivery-reminder-image">
    <button class="close-delivery-reminder" onclick="closeDeliveryReminder()">關閉提醒</button>
  </div>
</div>

<div id="successMessage" style="display: none; text-align: center; padding: 20px; background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.05)); border: 2px solid #27ae60; border-radius: 12px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
    <h3 style="color: #27ae60; margin-bottom: 16px; font-weight: bold;">訂單已提交！</h3>
    <p style="margin-bottom: 10px; color: #2c3e50;">感謝您的購買，我們將盡快處理您的訂單！</p>
    <p style="color: #e74c3c; margin-bottom: 14px; font-weight: bold;">請聯繫官方LINE或FB私訊聯繫 訂單是否無誤</p>
</div>

<!-- 添加提示框 HTML
<div id="orderAlert" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
     background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.3);
     z-index: 1001; text-align: center; max-width: 80%; width: 300px;">
    <p style="color: #3c763d; margin-bottom: 15px;">請聯繫官方LINE或FB私訊聯繫 訂單是否無誤</p>
    <button onclick="closeOrderAlert()"
            style="background: #4CAF50; color: white; border: none; padding: 10px 20px;
                   border-radius: 4px; cursor: pointer;">確定</button>
</div> -->

<script>
function calculateTotal() {
    let total = 0;
    let subtotal = 0;

    // 計算商品小計
    for (let i = 1; i <= 3; i++) {
        const quantity = parseInt(document.getElementById('quantity' + i).value) || 0;
        const priceMatch = document.getElementById('selectedItem' + i).textContent.match(/\$(\d+)/);
        if (priceMatch) {
            const price = parseInt(priceMatch[1]);
            subtotal += quantity * price;
        }
    }

    // 計算運費
    let shippingFee = 0;
    if (subtotal > 0 && subtotal < 350) {
        shippingFee = 100;
    }

    // 更新運費顯示
    document.getElementById('shippingFee').textContent = `$${shippingFee}`;
    document.getElementById('hiddenShippingFee').value = shippingFee;

    // 更新總金額（商品小計 + 運費）
    total = subtotal + shippingFee;
    document.getElementById('totalAmount').textContent = `$${total}`;
    document.getElementById('hiddenTotalAmount').value = total;

    // 更新父框架高度
    updateParentHeight();
}

    // 修改顯示地址區域的函數
    function showAdditionalInput() {
      var deliveryMethod = document.getElementById('storeLocation').value;
      var addressSection = document.getElementById('addressSection');
        var additionalInput = document.getElementById('additionalInput');
        var storeButton = document.getElementById('storeButton');
        var selectedStore = document.getElementById('selectedStore');
        var addressSelects = document.querySelector('.address-selects');

        if (deliveryMethod === '宅配到府' || deliveryMethod === '7-11門市') {
        addressSection.style.display = 'flex';

            if (deliveryMethod === '7-11門市') {
                addressSelects.style.display = 'none';  // 隱藏縣市和地區選單
                additionalInput.style.display = 'none';
                storeButton.style.display = 'inline-block';
                selectedStore.style.display = 'inline';
                document.getElementById('county').required = false;
            } else {
                addressSelects.style.display = 'flex';  // 顯示縣市和地區選單
                additionalInput.style.display = 'block';
                additionalInput.placeholder = '請輸入詳細地址';
                storeButton.style.display = 'none';
                selectedStore.style.display = 'none';
                document.getElementById('county').required = true;
            }
      } else {
        addressSection.style.display = 'none';
            document.getElementById('county').required = false;
            additionalInput.required = false;
        }

        // 載入對應的到貨日期選項
        loadAvailableDates(deliveryMethod);
    }

    // 添加縣市改變事件處理
    function onCountyChange() {
        var county = document.getElementById('county').value;
        var districtSelect = document.getElementById('districtSelect');

        // 根據縣市更新地區選選項
        if (county && districts[county]) {
            districtSelect.innerHTML = '<option value="">地區</option>' +
                districts[county].map(district =>
                    `<option value="${district}">${district}</option>`
                ).join('');
            districtSelect.style.display = 'block';
        } else {
            districtSelect.style.display = 'none';
      }
    }

    // 驗證電話號碼格式
    function validatePhoneNumber() {
      var phoneInput = document.getElementById('phone');
      var phoneError = document.getElementById('phoneError');
      var phoneValue = phoneInput.value;
      // 新的電話格式驗證：手機號碼或市話號碼
      var phonePattern = /^(09\d{8}|0[2-8]\d{7,8})$/;
      if (!phonePattern.test(phoneValue)) {
        phoneError.style.display = 'inline';
        phoneInput.classList.add('input-error');
        return false;
      } else {
        phoneError.style.display = 'none';
        phoneInput.classList.remove('input-error');
        return true;
      }
    }

// 添加一個防重複提交的變數
let isSubmitting = false;
let submitTimeout = null;

// 修改表單驗證和提交函數
function validateAndSubmitForm() {
    if (isSubmitting) {
        return false;
    }

    const errors = [];
    if (!validateQuantities(errors) || !validateRequiredFields(errors)) {
        showValidationPopup(errors);
        return false;
    }

    // 確保有 IP 地址
    if (!document.getElementById('userip').value) {
        document.getElementById('userip').value = 'ts_' + Date.now();
    }

    // 設置提交狀態
    isSubmitting = true;
    const submitButton = document.getElementById('submit-button');
    submitButton.disabled = true;
    submitButton.style.backgroundColor = '#cccccc';
    submitButton.textContent = '處理中...';

    // 顯示提示框
    setTimeout(() => {
        document.getElementById('orderAlert').style.display = 'block';
        // 清空表單
        clearForm();
    }, 1000);

    // 5秒後重置按鈕狀態
    setTimeout(() => {
        isSubmitting = false;
        submitButton.disabled = false;
        submitButton.style.backgroundColor = '';
        submitButton.textContent = '提交訂單';
    }, 5000);

    return true;
}

// 修改清空表單函數，添加隱藏成功訊息
function clearForm() {
    // 清空商品數量
    for (let i = 1; i <= 3; i++) {
        document.getElementById(`quantity${i}`).value = '';
    }

    // 清空個人資訊
    document.getElementById('name').value = '';
    document.getElementById('phone').value = '';
    document.getElementById('storeLocation').value = '';
    document.getElementById('deliveryDate').value = '';
    document.getElementById('facebooklineid').value = '';

    // 清空地址相關欄位
    if (document.getElementById('county')) {
        document.getElementById('county').value = '';
    }
    if (document.getElementById('districtSelect')) {
        document.getElementById('districtSelect').value = '';
        document.getElementById('districtSelect').style.display = 'none';
    }
    if (document.getElementById('additionalInput')) {
        document.getElementById('additionalInput').value = '';
    }

    // 清空門市選擇
    if (document.getElementById('selectedStore')) {
        document.getElementById('selectedStore').textContent = '';
    }

    // 清空付款方式
    document.getElementById('pay').value = '';

    // 隱藏銀行轉帳資訊
    if (document.getElementById('bankInfo')) {
        document.getElementById('bankInfo').style.display = 'none';
    }

    // 清空備註
    document.getElementById('notes').value = '';

    // 重置所有錯誤樣式
    document.querySelectorAll('.input-error').forEach(element => {
        element.classList.remove('input-error', 'shake');
        element.style.borderColor = '';
        element.style.backgroundColor = '';
    });

    // 重置總金額顯示
    document.getElementById('totalAmount').textContent = '$0';
    document.getElementById('shippingFee').textContent = '$0';

    // 更新父框架高度
    updateParentHeight();
}

// 載入可選擇的到貨日期
async function loadAvailableDates(deliveryMethod) {
    const deliveryDateSelect = document.getElementById('deliveryDate');

    // 清空現有選項
    deliveryDateSelect.innerHTML = '<option value="">載入中...</option>';

    if (!deliveryMethod) {
        deliveryDateSelect.innerHTML = '<option value="">請先選擇配送方式</option>';
        return;
    }

    try {
        // 根據配送方式轉換為 API 參數
        let apiDeliveryMethod = '';
        if (deliveryMethod === '宅配到府') {
            apiDeliveryMethod = '宅配到府';
        } else if (deliveryMethod === '7-11門市') {
            apiDeliveryMethod = '超商取貨';
        }

        const response = await fetch(`../api/delivery_settings.php?action=available_dates&delivery_method=${encodeURIComponent(apiDeliveryMethod)}`);
        const result = await response.json();

        if (result.success && result.data) {
            deliveryDateSelect.innerHTML = '<option value="">請選擇到貨日期</option>';

            result.data.forEach(dateOption => {
                const option = document.createElement('option');
                option.value = dateOption.value || dateOption;
                option.textContent = dateOption.display || dateOption;
                deliveryDateSelect.appendChild(option);
            });
        } else {
            deliveryDateSelect.innerHTML = '<option value="">無可選日期</option>';
            console.error('載入日期失敗:', result.message);
        }
    } catch (error) {
        console.error('載入日期錯誤:', error);
        deliveryDateSelect.innerHTML = '<option value="">載入失敗</option>';
    }
}

// 添加日期驗證函數
function validateDeliveryDate(input) {
    var selectedDate = new Date(input.value);
    var today = new Date();
    var minDate = new Date(today);
    minDate.setDate(today.getDate() + 0); // 設定最小日期為明天

    // 如果沒有選擇日期，直接返回
    if (!input.value) {
        return true;
    }

    // 計算選擇日期和今天的差距（天數）
    var diffTime = selectedDate.getTime() - today.getTime();
    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // 檢查是否在允許範圍內
    if (selectedDate < minDate) {
        showValidationPopup(['請選擇明天後的日期']);
        input.classList.add('input-error', 'shake');
        input.value = '';
        return false;
    }

    // 如果選擇的日期在3天內，顯示提示訊息
    if (diffDays <= 3) {
        showValidationPopup(
            ['提醒：3天內的訂單，若當日訂單已滿，將自動順延至下一個工作天出貨。<br><br>例如您指定18日到貨，我們必須在17日出貨，若17日無法出貨將會自動改為18日出貨(最快)19日到貨，以此類推。'],
            '提示：訂單處理說明'
        );
    }

    // 特定日期陣列（格式：YYYY-MM-DD）
    var excludedDates = [
        '2025-02-28', // 和平紀念日
        '2025-04-04', // 兒童節
        '2025-04-05', // 清明節
        '2025-01-28', // 除夕
        '2025-01-29', // 初一
        '2025-01-30', // 初二
        '2025-01-31', // 初三
    ];

    // 檢查是否為排除日期
    if (excludedDates.includes(input.value)) {
        showValidationPopup(['此日期宅配不提供配送服務']);
        input.classList.add('input-error', 'shake');
        input.value = '';
        return false;
    }

    input.classList.remove('input-error', 'shake');
    return true;
}

// 修改開啟門市選擇器的函數
function openStoreSelector() {
    var url = 'store-selector.php';
    document.getElementById('storePickerFrame').src = url;
    document.getElementById('storePickerOverlay').style.display = 'block';
    document.getElementById('storePicker').style.display = 'block';
    document.body.style.overflow = 'hidden'; // 防止背景滾動
}

// 添加關閉門市選擇器的函數
function closeStorePicker() {
    document.getElementById('storePickerOverlay').style.display = 'none';
    document.getElementById('storePicker').style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢復背景滾動
    document.getElementById('reminderOverlay').style.display = 'flex';
}

function setStoreInfo(storeName, storeAddress, storeId) {
    // 從地址中提取縣市和地區
    var match = storeAddress.match(/^([^市縣]+[市縣])([^區鄉鎮市]+[區鄉鎮市])/);
    var formattedStore = '';

    if (match) {
        var county = match[1];
        var district = match[2];
        formattedStore = `${county}-${district}<${storeName}>門市 ${storeId}`;
    } else {
        formattedStore = `<${storeName}>門市 ${storeId}`;
    }

    document.getElementById('selectedStore').textContent = formattedStore;
    // 將格式化後的門市資訊存入 additionalInput，這樣會傳到 Google Sheet
    document.getElementById('additionalInput').value = formattedStore;

    if (!document.getElementById('fullStoreAddress')) {
        var hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.id = 'fullStoreAddress';
        hiddenInput.name = 'fullStoreAddress';
        document.getElementById('addressSection').appendChild(hiddenInput);
    }
    document.getElementById('fullStoreAddress').value = storeAddress;
}

// 修改宅配到府地址格式化函數
function formatHomeDeliveryAddress() {
    var county = document.getElementById('county').value;
    var district = document.getElementById('districtSelect').value;
    var address = document.getElementById('additionalInput').value;

    if (county && district && address) {
        // 檢查地址是否已經包含縣市和地區
        var cleanAddress = address;
        // 移除可能已存在的縣市和地區前綴
        var prefixPattern = new RegExp(`^${county}(-${county})?-${district}\\s*`);
        cleanAddress = cleanAddress.replace(prefixPattern, '');

        // 組合新的地址格式
        document.getElementById('additionalInput').value = `${county}-${district} ${cleanAddress}`;
    }
}

// 地址驗證數據庫 - 基於33筆客戶地址分析
const addressDatabase = {
    // 常見地址格式模式
    patterns: [
        /^([^市縣]+[市縣])-([^區鄉鎮市]+[區鄉鎮市])\s+(.+)$/,  // 標準格式：縣市-區域 詳細地址
        /^([^市縣]+[市縣])([^區鄉鎮市]+[區鄉鎮市])(.+)$/,      // 無分隔符：縣市區域詳細地址
        /^([^市縣]+[市縣])-([^區鄉鎮市]+[區鄉鎮市])(.+)$/       // 部分分隔：縣市-區域詳細地址
    ],

    // 常見錯誤和建議
    commonErrors: {
        '台北': '台北市',
        '新北': '新北市',
        '桃園': '桃園市',
        '台中': '台中市',
        '台南': '台南市',
        '高雄': '高雄市'
    },

    // 必要元素檢查 - 只驗證門牌號碼（縣市地區已用下拉選單確保）
    requiredElements: {
        hasNumber: /\d+號/,                  // 必須包含門牌號碼(XX號)
    },

};

// 地址驗證主函數
function validateAddress() {
    const addressInput = document.getElementById('additionalInput');
    const county = document.getElementById('county').value;
    const district = document.getElementById('districtSelect').value;
    const address = addressInput.value.trim();
    const resultDiv = document.getElementById('addressValidationResult');

    // 清空之前的驗證結果
    resultDiv.innerHTML = '';

    // 如果是7-11門市模式，不進行地址驗證
    const storeLocation = document.getElementById('storeLocation').value;
    if (storeLocation === '7-11門市') {
        return;
    }

    // 如果地址為空，不顯示驗證結果
    if (!address) {
        return;
    }

    const validationResults = performAddressValidation(county, district, address);
    displayValidationResults(validationResults, resultDiv);

    // 更新父框架高度
    updateParentHeight();
}

// 執行地址驗證邏輯
function performAddressValidation(county, district, address) {
    const results = {
        level: 'info', // info, warning, error
        messages: [],
        suggestions: []
    };

    // 1. 檢查基本格式
    if (!county || !district) {
        results.level = 'error';
        results.messages.push('請先選擇縣市和地區');
        return results;
    }

    // 2. 檢查必要元素 - 只驗證門牌號碼
    if (!addressDatabase.requiredElements.hasNumber.test(address)) {
        results.level = 'error';
        results.messages.push('地址必須包含門牌號碼(例：123號)');
    }

    return results;
}

// 顯示驗證結果
function displayValidationResults(results, container) {
    let html = '';

    // 顯示主要訊息
    if (results.messages.length > 0) {
        const className = `address-validation-${results.level}`;
        html += `<div class="${className}">`;
        results.messages.forEach(message => {
            html += `<div>📍 ${message}</div>`;
        });
        html += '</div>';
    }

    // 顯示建議
    if (results.suggestions.length > 0) {
        html += '<div class="address-validation-info">';
        html += '<div>💡 格式建議：</div>';
        results.suggestions.forEach(suggestion => {
            html += `<div class="address-suggestion" onclick="applySuggestion('${suggestion}')">${suggestion}</div>`;
        });
        html += '</div>';
    }

    container.innerHTML = html;
}

// 應用建議的地址格式
function applySuggestion(suggestion) {
    const addressInput = document.getElementById('additionalInput');

    // 直接應用道路門牌建議
    addressInput.value = suggestion;

    // 重新驗證
    validateAddress();
}

// 在 script 標籤內添加以下代碼
const districts = {
    '台北市': ['中正區', '大同區', '中山區', '松山區', '大安區', '萬華區', '信義區', '士林區', '北投區', '內湖區', '南港區', '文山區'],
    '新北市': ['板橋區', '三重區', '中和區', '永和區', '新莊區', '新店區', '樹林區', '鶯歌區', '三峽區', '淡水區', '汐止區', '瑞芳區', '土城區', '蘆洲區', '五股區', '泰山區', '林口區', '深坑區', '石碇區', '坪林區', '三芝區', '石門區', '八里區', '平溪區', '雙溪區', '貢寮區', '金山區', '萬里區', '烏來區'],
    '基隆市': ['仁愛區', '信義區', '中正區', '中山區', '安樂區', '暖暖區', '七堵區'],
    '桃園市': ['桃園區', '中壢區', '平鎮區', '八德區', '楊梅區', '蘆竹區', '大溪區', '龜山區', '大園區', '觀音區', '新屋區', '龍潭區', '復興區'],
    '新竹市': ['東區', '北區', '香山區'],
    '新竹縣': ['竹北市', '竹東鎮', '新埔鎮', '關西鎮', '湖口鄉', '新豐鄉', '峨眉鄉', '寶山鄉', '北埔鄉', '芎林鄉', '橫山鄉', '尖石鄉', '五峰鄉'],
    '苗栗縣': ['苗栗市', '頭份市', '竹南鎮', '後龍鎮', '通霄鎮', '苑裡鎮', '卓蘭鎮', '造橋鄉', '西湖鄉', '頭屋鄉', '公館鄉', '銅鑼鄉', '三義鄉', '大湖鄉', '獅潭鄉', '三灣鄉', '南庄鄉', '泰安鄉'],
    '台中市': ['中區', '東區', '南區', '西區', '北區', '北屯區', '西屯區', '南屯區', '太平區', '大里區', '霧峰區', '烏日區', '豐原區', '后里區', '石岡區', '東勢區', '和平區', '新社區', '潭子區', '大雅區', '神岡區', '大肚區', '沙鹿區', '龍井區', '梧棲區', '清水區', '大甲區', '外埔區', '大安區'],
    '彰化縣': ['彰化市', '員林市', '和美鎮', '鹿港鎮', '溪湖鎮', '二林鎮', '田中鎮', '北斗鎮', '花壇鄉', '芬園鄉', '大村鄉', '永靖鄉', '伸港鄉', '線西鄉', '福興鄉', '秀水鄉', '埔心鄉', '埔鹽鄉', '大城鄉', '芳苑鄉', '竹塘鄉', '社頭鄉', '二水鄉', '田尾鄉', '埤頭鄉', '溪州鄉'],
    '南投縣': ['南投市', '埔里鎮', '草屯鎮', '竹山鎮', '集集鎮', '名間鄉', '鹿谷鄉', '中寮鄉', '魚池鄉', '國姓鄉', '水里鄉', '信義鄉', '仁愛鄉'],
    '雲林縣': ['斗六市', '斗南鎮', '虎尾鎮', '西螺鎮', '土庫鎮', '北港鎮', '古坑鄉', '大埤鄉', '莿桐鄉', '林內鄉', '二崙鄉', '崙背鄉', '麥寮鄉', '東勢鄉', '褒忠鄉', '台西鄉', '元長鄉', '四湖鄉', '口湖鄉', '水林鄉'],
    '嘉義市': ['東區', '西區'],
    '嘉義縣': ['太保市', '朴子市', '布袋鎮', '大林鎮', '民雄鄉', '溪口鄉', '新港鄉', '六腳鄉', '東石鄉', '義竹鄉', '鹿草鄉', '水上鄉', '中埔鄉', '竹崎鄉', '梅山鄉', '番路鄉', '大埔鄉', '阿里山鄉'],
    '台南市': ['中西區', '東區', '南區', '北區', '安平區', '安南區', '永康區', '歸仁區', '新化區', '左鎮區', '玉井區', '楠西區', '南化區', '仁德區', '關廟區', '龍崎區', '官田區', '麻豆區', '佳里區', '西港區', '七股區', '將軍區', '學甲區', '北門區', '新營區', '後壁區', '白河區', '東山區', '六甲區', '下營區', '柳營區', '鹽水區', '善化區', '大內區', '山上區', '新市區', '安定區'],
    '屏東縣': ['屏東市', '潮州鎮', '東港鎮', '恆春鎮', '萬丹鄉', '長治鄉', '麟洛鄉', '九如鄉', '里港鄉', '鹽埔鄉', '高樹鄉', '萬巒鄉', '內埔鄉', '竹田鄉', '新埤鄉', '枋寮鄉', '新園鄉', '崁頂鄉', '林邊鄉', '南州鄉', '佳冬鄉', '琉球鄉', '車城鄉', '滿州鄉', '枋山鄉', '三地門鄉', '霧臺鄉', '瑪家鄉', '泰武鄉', '來義鄉', '春日鄉', '獅子鄉', '牡丹鄉'],
    '宜蘭縣': ['宜蘭市', '羅東鎮', '蘇澳鎮', '頭城鎮', '礁溪鄉', '壯圍鄉', '員山鄉', '冬山鄉', '五結鄉', '三星鄉', '大同鄉', '南澳鄉'],
    '花蓮縣': ['花蓮市', '鳳林鎮', '玉里鎮', '新城鄉', '吉安鄉', '壽豐鄉', '光復鄉', '豐濱鄉', '瑞穗鄉', '富里鄉', '秀林鄉', '萬榮鄉', '卓溪鄉'],
    '台東縣': ['台東市', '成功鎮', '關山鎮', '卑南鄉', '鹿野鄉', '池上鄉', '東河鄉', '長濱鄉', '太麻里鄉', '大武鄉', '綠島鄉', '海端鄉', '延平鄉', '金峰鄉', '達仁鄉', '蘭嶼鄉'],
    '澎湖縣': ['馬公市', '湖西鄉', '白沙鄉', '西嶼鄉', '望安鄉', '七美鄉'],
    '金門縣': ['金城鎮', '金湖鎮', '金沙鎮', '金寧鄉', '烈嶼鄉', '烏坵鄉'],
    '連江縣': ['南竿鄉', '北竿鄉', '莒光鄉', '東引鄉'],
    '高雄市': ['楠梓區', '左營區', '鼓山區', '三民區', '苓雅區', '新興區', '前金區', '前鎮區', '旗津區', '小港區', '鳳山區', '大寮區', '鳥松區', '林園區', '仁武區', '大樹區', '大社區', '岡山區', '路竹區', '橋頭區', '梓官區', '彌陀區', '永安區', '燕巢區', '田寮區', '阿蓮區', '茄萣區', '湖內區', '旗山區', '美濃區', '內門區', '杉林區', '甲仙區', '六龜區', '茂林區', '桃源區', '那瑪夏區']
};

// 在文檔加載完成後執行
document.addEventListener('DOMContentLoaded', function() {
    // 發送消息給父框架，通知實際內容高度
    function updateParentHeight() {
        const height = document.body.scrollHeight;
        window.parent.postMessage({
            'type': 'resize',
            'height': height
        }, '*');
    }

    // 監聽內容變化
    const observer = new MutationObserver(updateParentHeight);
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 初始更新
    updateParentHeight();
});

function validateForm() {
    let isValid = true;
    const errors = [];

    // 檢查商品數量
    let hasItems = false;
    for (let i = 1; i <= 3; i++) {
        const quantity = document.getElementById('quantity' + i).value;
        if (quantity > 0) {
            hasItems = true;
            break;
        }
    }
    if (!hasItems) {
        errors.push('請選擇至少一項商品數量');
        isValid = false;
    }

    // 檢查必填欄位
    const requiredFields = [
        { id: 'name', label: '姓名' },
        { id: 'phone', label: '電話' },
        { id: 'storeLocation', label: '取貨方式' },
        { id: 'deliveryDate', label: '希望到貨日' },
        { id: 'pay', label: '付款方式' }
    ];

    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element.value.trim()) {
            errors.push(`請填寫${field.label}`);
            element.classList.add('input-error', 'shake');
            isValid = false;
        } else {
            element.classList.remove('input-error', 'shake');
        }
    });

    if (!isValid) {
        showValidationPopup(errors);
    }

    return isValid;
}

function showValidationPopup(errors, customTitle) {
    const errorsList = document.getElementById('validationErrors');
    const titleElement = document.getElementById('validationTitle');

    if (customTitle) {
        // 自定義提示訊息的樣式
        titleElement.textContent = customTitle;
        titleElement.style.color = '#333';
        errorsList.style.listStyle = 'none';
        errorsList.style.paddingLeft = '0';
        errorsList.innerHTML = errors[0];
    } else {
        // 驗證錯誤的樣式
        titleElement.textContent = '請填寫以下必填項目：';
        titleElement.style.color = '#ff3333';
        errorsList.style.listStyle = 'disc';
        errorsList.style.paddingLeft = '20px';
        errorsList.innerHTML = errors.map(error => `<li>${error}</li>`).join('');
    }

    document.getElementById('validationOverlay').style.display = 'block';
}

function closeValidationPopup() {
    document.getElementById('validationOverlay').style.display = 'none';
}

// 移除動畫效果
document.addEventListener('animationend', function(e) {
    if (e.target.classList.contains('shake')) {
        e.target.classList.remove('shake');
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // 防止觸摸事件傳播到父層
    document.body.addEventListener('touchmove', function(e) {
        const element = e.target;
        const isAtTop = element.scrollTop === 0;
        const isAtBottom = element.scrollHeight - element.scrollTop === element.clientHeight;

        // 如果內容已經滾動到頂部或底部，則允許事件傳播到父層
        if ((isAtTop && e.touches[0].clientY > 0) ||
            (isAtBottom && e.touches[0].clientY < 0)) {
            e.stopPropagation();
        }
    }, { passive: false });
});

// 整合所有的初始化代碼
document.addEventListener('DOMContentLoaded', function() {
    initializeFormValidation();
    initializeScrollBehavior();
    initializeHeightUpdates();
    initializeInputValidation();
});

// 表單驗證初始化
function initializeFormValidation() {
    document.getElementById('orderForm').onsubmit = function(e) {
        return validateAndSubmitForm(e);
    };

    // 移除動畫效果
    document.addEventListener('animationend', function(e) {
        if (e.target.classList.contains('shake')) {
            e.target.classList.remove('shake');
        }
    });
}

// 滾動行為初始化
function initializeScrollBehavior() {
    document.body.addEventListener('touchmove', function(e) {
        const element = e.target;
        const isAtTop = element.scrollTop === 0;
        const isAtBottom = element.scrollHeight - element.scrollTop === element.clientHeight;

        if ((isAtTop && e.touches[0].clientY > 0) ||
            (isAtBottom && e.touches[0].clientY < 0)) {
            e.stopPropagation();
        }
    }, { passive: false });
}

// 高度更新初始化
function initializeHeightUpdates() {
    const observer = new MutationObserver(updateParentHeight);
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    updateParentHeight();
}

// 表單驗證函數
function validateAndSubmitForm(e) {
    let isValid = true;
    const errors = [];

    // 檢查商品數量
    if (!validateQuantities(errors)) {
        isValid = false;
    }

    // 檢查必填欄位
    if (!validateRequiredFields(errors)) {
        isValid = false;
    }

    if (!isValid) {
        e.preventDefault();
        showValidationPopup(errors);
        return false;
    }

    return true;
}

// 驗證商品數量
function validateQuantities(errors) {
    let hasItems = false;
    let totalQuantity = 0;

    for (let i = 1; i <= 3; i++) {
        const quantityInput = document.getElementById('quantity' + i);
        const quantity = parseInt(quantityInput.value) || 0;
        totalQuantity += quantity;

        if (quantity < 0) {
            quantityInput.classList.add('input-error', 'shake');
            quantityInput.style.borderColor = '#ff0000';
            quantityInput.style.backgroundColor = '#fff0f0';
        } else {
            quantityInput.style.borderColor = '';
            quantityInput.style.backgroundColor = '';
        }
    }

    if (totalQuantity === 0) {
        errors.push('請至少選購一項商品');
        // 為所有數量輸入添加紅色提醒
        for (let i = 1; i <= 3; i++) {
            const input = document.getElementById('quantity' + i);
            input.classList.add('input-error', 'shake');
            input.style.borderColor = '#ff0000';
            input.style.backgroundColor = '#fff0f0';
        }
        hasItems = false;
    } else {
        hasItems = true;
        // 移除所有數量輸入的錯誤樣式
        for (let i = 1; i <= 3; i++) {
            const input = document.getElementById('quantity' + i);
            input.classList.remove('input-error', 'shake');
            input.style.borderColor = '';
            input.style.backgroundColor = '';
        }
    }

    return hasItems;
}

// 驗證必填欄位
function validateRequiredFields(errors) {
    let isValid = true;
    const requiredFields = [
        { id: 'name', label: '姓名' },
        { id: 'phone', label: '電話' },
        { id: 'storeLocation', label: '取貨方式' },
        { id: 'deliveryDate', label: '希望到貨日' },
        { id: 'pay', label: '付款方式' }
    ];

    // 檢查宅配地址相關欄位
    const storeLocation = document.getElementById('storeLocation').value;
    if (storeLocation === '宅配到府') {
        const county = document.getElementById('county');
        const districtSelect = document.getElementById('districtSelect');
        const additionalInput = document.getElementById('additionalInput');

        if (!county.value) {
            errors.push('請選擇縣市');
            county.classList.add('input-error', 'shake');
            isValid = false;
        }

        if (!districtSelect.value) {
            errors.push('請選擇地區');
            districtSelect.classList.add('input-error', 'shake');
            isValid = false;
        }

        if (!additionalInput.value.trim()) {
            errors.push('請輸入詳細地址');
            additionalInput.classList.add('input-error', 'shake');
            isValid = false;
        } else {
            // 執行地址驗證
            const addressValidation = performAddressValidation(county.value, districtSelect.value, additionalInput.value.trim());
            if (addressValidation.level === 'error') {
                errors.push('地址格式不正確，請檢查並修正');
                additionalInput.classList.add('input-error', 'shake');
                isValid = false;
            }
        }
    } else if (storeLocation === '7-11門市') {
        const selectedStore = document.getElementById('selectedStore');
        const additionalInput = document.getElementById('additionalInput');

        if (!selectedStore.textContent || !additionalInput.value) {
            errors.push('請選擇 7-11 門市');
            document.getElementById('storeButton').classList.add('input-error', 'shake');
            isValid = false;
        }
    }

    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element.value.trim()) {
            errors.push(`請填寫${field.label}`);
            element.classList.add('input-error', 'shake');
            isValid = false;
        } else {
            element.classList.remove('input-error', 'shake');
        }
    });

    return isValid;
}

// 顯示驗證提示視窗
function showValidationPopup(errors) {
    const errorsList = document.getElementById('validationErrors');
    errorsList.innerHTML = errors.map(error => `<li>${error}</li>`).join('');
    document.getElementById('validationOverlay').style.display = 'block';
}

// 關閉驗證提示視窗
function closeValidationPopup() {
    document.getElementById('validationOverlay').style.display = 'none';
}

// 更新父框架高度
function updateParentHeight() {
    const height = document.body.scrollHeight;
    window.parent.postMessage({
        'type': 'resize',
        'height': height
    }, '*');
}

// 添加監聽所有必填欄位的函數
function initializeInputValidation() {
    const requiredFields = [
        'name',
        'phone',
        'storeLocation',
        'deliveryDate',
        'county',
        'districtSelect',
        'additionalInput',
        'pay'
    ];

    requiredFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.addEventListener('input', function() {
                if (this.value.trim()) {
                    this.classList.remove('input-error', 'shake');
                    this.style.borderColor = '';
                    this.style.backgroundColor = '';
                }
            });
            // 為下拉選單添加 change 事件監聽
            if (element.tagName === 'SELECT') {
                element.addEventListener('change', function() {
                    if (this.value) {
                        this.classList.remove('input-error', 'shake');
                        this.style.borderColor = '';
                        this.style.backgroundColor = '';
                    }
                });
            }
        }
    });

    // 特別處理數量輸入
    for (let i = 1; i <= 3; i++) {
        const quantityInput = document.getElementById('quantity' + i);
        if (quantityInput) {
            quantityInput.addEventListener('input', function() {
                const quantity = parseInt(this.value) || 0;
                if (quantity > 0) {
                    this.classList.remove('input-error', 'shake');
                    this.style.borderColor = '';
                    this.style.backgroundColor = '';
                }
                validateQuantities([]);
            });
        }
    }
}

function closeReminder() {
  document.getElementById('reminderOverlay').style.display = 'none';
}

// 修改宅配選項的監聽函數
document.getElementById('storeLocation').addEventListener('change', function() {
  if (this.value === '宅配到府') {
    document.getElementById('deliveryReminderOverlay').style.display = 'flex';
  }
});

// 關閉宅配提醒視窗
function closeDeliveryReminder() {
  document.getElementById('deliveryReminderOverlay').style.display = 'none';
}

// 監聽付款方式的變更
document.getElementById('pay').addEventListener('change', function() {
  const bankInfo = document.getElementById('bankInfo');
  if (this.value === '轉帳匯款') {
    bankInfo.style.display = 'block';
  } else {
    bankInfo.style.display = 'none';
  }
});

// 發送高度更新到父頁面
function updateParentHeight() {
  const height = document.body.scrollHeight;
  window.parent.postMessage({
    type: 'resize',
    height: height
  }, '*');
}

// 在以下情況觸發高度更新
window.addEventListener('load', updateParentHeight);
window.addEventListener('resize', updateParentHeight);

// 監聽表單內容變化
const formElements = document.querySelectorAll('input, select, textarea');
formElements.forEach(element => {
  element.addEventListener('change', updateParentHeight);
  element.addEventListener('input', updateParentHeight);
});

// 監聽展開/收起的元素
const expandableElements = document.querySelectorAll('#bankInfo, #addressSection');
expandableElements.forEach(element => {
  const observer = new MutationObserver(updateParentHeight);
  observer.observe(element, {
    attributes: true,
    attributeFilter: ['style'],
    subtree: true
  });
});

// 添加關閉提示框的函數
function closeOrderAlert() {
    document.getElementById('orderAlert').style.display = 'none';
}

// 添加確認對話框函數
function showOrderConfirmation(formData) {
    const dialog = document.createElement('div');
    dialog.className = 'modal-overlay';

    // 獲取表單數據
    const name = formData.get('name');
    const phone = formData.get('phone');
    const storeLocation = formData.get('storeLocation');
    const address = formData.get('additionalInput');
    const deliveryDate = formData.get('deliveryDate');
    const facebookline = formData.get('facebookline');
    const facebooklineid = formData.get('facebooklineid');
    const pay = formData.get('pay');

    // 獲取商品數量和總金額
    const items = [];
    const totalAmount = formData.get('totalAmount');
    const shippingFee = formData.get('shippingFee') || calculateShippingFee(totalAmount);
    const finalTotal = formData.get('finalTotal') || (Number(totalAmount));

    for (let i = 1; i <= 3; i++) {
        const quantity = formData.get('quantity' + i);
        if (quantity > 0) {
            const itemSelection = formData.get('itemSelection' + i);
            items.push(`${itemSelection} x ${quantity}`);
        }
    }

    dialog.innerHTML = `
        <div class="modal-content" style="max-width: 340px;">
            <h3 style="color: #8b4513; text-align: center;">訂單確認</h3>
            <div style="background-color: #fff9e6; padding: 15px; border-radius: 8px; margin: 10px 0;">
                <p><strong>姓名：</strong>${name}</p>
                <p><strong>電話：</strong>${phone}</p>
                <p><strong>取貨方式：</strong>${storeLocation}</p>
                <p><strong>地址/門市：</strong>${address}</p>
                <p><strong>希望到貨日：</strong>${deliveryDate}</p>
                <p><strong>聯絡方式ID：</strong>${facebookline} ； ${facebooklineid}</p>
                <p><strong>付款方式：</strong>${pay}</p>
                <p><strong>訂購商品：</strong></p>
                <ul style="margin: 5px 0; padding-left: 20px; color: #880e4f">
                    ${items.map(item => `<li>${item}</li>`).join('')}
                </ul>
                <p style="color: #ff0000; font-weight: bold;">運費：${shippingFee} 元${shippingFee === 0 ? ' (免運費)' : ''}</p>
                <p style="color: #ff0000; font-weight: bold; font-size: 1.2em;">總計：${finalTotal} 元</p>
            </div>
            <div style="color: #ff0000; margin: 10px 0; text-align: center;">
                <p>送出訂單後，請務必'臉書私訊'或'LINE訊息'或'撥電話'給我們，確認訂單是否完成。以便我們盡快出貨。</p>
            </div>
            <div style="display: flex; gap: 10px; justify-content: center; margin-top: 15px;">
                <button onclick="this.closest('.modal-overlay').remove();"
                        style="background-color: #f44336; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                    返回修改
                </button>
                <button onclick="submitConfirmedOrder(this)"
                        style="background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                    送出並前往官方LINE確認
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);
    dialog.style.display = 'block';
}

// 修改表單提交處理函數
function validateAndSubmitForm() {
    const errors = [];
    if (!validateQuantities(errors) || !validateRequiredFields(errors)) {
        showValidationPopup(errors);
        return false;
    }

    // 獲取表單數據
    const form = document.getElementById('orderForm');
    const formData = new FormData(form);

    // 顯示確認對話框
    showOrderConfirmation(formData);

    return false; // 阻止表單直接提交
}

// 修改確認後提交函數
function submitConfirmedOrder(button) {
    try {

        // 禁用按鈕防止重複提交
        button.disabled = true;
        button.textContent = '提交中...';

        // 獲取原始表單
        const originalForm = document.getElementById('orderForm');
        if (!originalForm) {
            throw new Error('找不到表單元素');
        }

        // 使用 fetch API 提交表單
        const formData = new FormData(originalForm);

        // 添加AJAX請求標頭
        fetch(originalForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('網絡響應不正常');
            }
            return response.json();
        })
        .then(data => {
            // 關閉確認對話框
            const modal = button.closest('.modal-overlay');
            if (modal) {
                modal.remove();
            }

            // 處理成功響應
            if (data.success) {
                // 顯示成功訊息
                showSuccessMessage();
                // 清空表單
                clearForm();
            } else {
                // 顯示錯誤訊息
                showErrorPopup(data.message || '提交失敗，請稍後再試');
            }
        })
        .catch(error => {
            console.error('提交訂單時發生錯誤:', error);
            showErrorPopup('提交失敗，請稍後再試或聯繫客服');
        })
        .finally(() => {
            // 重置按鈕狀態
            button.disabled = false;
            button.textContent = '確認送出';
        });
        // 開啟 LINE 連結
        window.open('https://line.me/R/ti/p/@218oxpgu', '_blank');

    } catch (error) {
        console.error('提交訂單時發生錯誤:', error);
        showErrorPopup('提交訂單時發生錯誤，請稍後再試');

        // 重置按鈕狀態
        button.disabled = false;
        button.textContent = '確認送出';
    }
}

// 添加成功訊息顯示函數
function showSuccessMessage() {
    const message = document.createElement('div');
    message.className = 'modal-overlay';
    message.innerHTML = `
        <div class="modal-content">
            <h3 style="color: #4CAF50;">訂單提交成功！</h3>
            <p>感謝您的購買，我們將盡快處理您的訂單。</p>
            <p>請保持電話暢通，以便我們聯繫您。</p>
            <button onclick="this.closest('.modal-overlay').remove();"
                    style="background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                確定
            </button>
        </div>
    `;
    document.body.appendChild(message);
    message.style.display = 'block';
}

// 修改清空表單函數
function clearForm() {
    // 清空數量輸入
    for (let i = 1; i <= 3; i++) {
        document.getElementById('quantity' + i).value = '0';
    }

    // 清空其他輸入欄位
    document.getElementById('name').value = '';
    document.getElementById('phone').value = '';
    document.getElementById('storeLocation').value = '';
    document.getElementById('deliveryDate').value = '';
    document.getElementById('facebooklineid').value = '';
    document.getElementById('pay').value = '';
    document.getElementById('additionalInput').value = '';

    // 更新總金額
    updateTotal();
}

// 修改計算運費的函數
function calculateShippingFee(totalAmount) {
    const amount = Number(totalAmount);
    // 訂單滿 350 免運費
    if (amount >= 350) {
        return 0;  // 滿350免運費
    }
    // 未滿條件則收取運費
    return 100;
}

// 在頁面加載時獲取 IP 地址
window.addEventListener('load', function() {
    // 使用 ipify API 獲取用戶 IP
    fetch('https://api.ipify.org?format=json')
        .then(response => response.json())
        .then(data => {
            document.getElementById('userip').value = data.ip;
        })
        .catch(error => {
            console.error('獲取 IP 地址失敗:', error);
            // 如果獲取失敗，使用時間戳作為備用標識符
            document.getElementById('userip').value = 'ts_' + Date.now();
        });
});

// 處理表單提交
function handleFormSubmit(event) {
  event.preventDefault();
  // 顯示提交中訊息
  var submitButton = document.querySelector('button[type="submit"]');
  var originalButtonText = submitButton.innerHTML;
  submitButton.innerHTML = '提交中...';
  submitButton.disabled = true;

  // 表單驗證
  var form = document.getElementById('orderForm');
  if (!form.checkValidity()) {
    form.reportValidity();
    submitButton.innerHTML = originalButtonText;
    submitButton.disabled = false;
    return false;
  }

  // 檢查是否至少選擇了一個產品
  var quantity1 = parseInt(document.getElementById('quantity1').value) || 0;
  var quantity2 = parseInt(document.getElementById('quantity2').value) || 0;
  var quantity3 = parseInt(document.getElementById('quantity3').value) || 0;

  if (quantity1 <= 0 && quantity2 <= 0 && quantity3 <= 0) {
    alert('請至少選擇一項產品');
    submitButton.innerHTML = originalButtonText;
    submitButton.disabled = false;
    return false;
  }

  // 開啟 LINE 連結
  //window.open('line://ti/p/@218oxpgu', '_blank');

  // 使用 fetch API 提交表單
  var formData = new FormData(form);

  // 添加AJAX請求標頭
  var fetchOptions = {
    method: 'POST',
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  };

  // 先隱藏可能存在的錯誤彈窗
  const existingOverlays = document.querySelectorAll('.modal-overlay');
  existingOverlays.forEach(overlay => overlay.remove());

  // 設置一個標誌，用於跟踪是否已經顯示了成功訊息
  let successShown = false;

  fetch(form.action, fetchOptions)
    .then(response => {
      if (!response.ok) {
        throw new Error('網絡響應不正常');
      }
      return response.json();
    })
    .then(data => {
      console.log('伺服器回應:', data); // 添加日誌以便調試

      // 處理成功響應
      if (data.success) {
        // 標記已顯示成功訊息
        successShown = true;
        // 顯示成功訊息
        showSuccessMessage();
        // 清空表單
        form.reset();
        calculateTotal();
      } else if (!successShown) {
        // 只有在未顯示成功訊息的情況下才顯示錯誤訊息
        showErrorPopup(data.message || '提交失敗，請稍後再試');
      }
    })
    .catch(error => {
      console.error('提交訂單時發生錯誤:', error);
      // 只有在沒有顯示成功訊息的情況下才顯示錯誤
      if (document.getElementById('successMessage').style.display !== 'block') {
        showErrorPopup('提交失敗，請稍後再試或聯繫客服');
      }
    })
    .finally(() => {
      // 恢復按鈕狀態
      submitButton.innerHTML = originalButtonText;
      submitButton.disabled = false;
    });

  return false;
}

// 顯示成功訊息
function showSuccessMessage() {
  document.getElementById('orderForm').style.display = 'none';
  document.getElementById('successMessage').style.display = 'block';

  // 滾動到成功訊息
  document.getElementById('successMessage').scrollIntoView({ behavior: 'smooth' });

}

// 顯示錯誤彈窗
function showErrorPopup(message) {
  // 檢查是否已經顯示成功訊息，如果已顯示則不再顯示錯誤
  if (document.getElementById('successMessage').style.display === 'block') {
    console.log('已顯示成功訊息，不再顯示錯誤');
    return;
  }

  // 移除可能已存在的錯誤彈窗
  const existingOverlays = document.querySelectorAll('.modal-overlay');
  existingOverlays.forEach(overlay => overlay.remove());

  const errorOverlay = document.createElement('div');
  errorOverlay.className = 'modal-overlay';
  errorOverlay.style.display = 'block';

  errorOverlay.innerHTML = `
    <div class="modal-content">
      <h3 style="color: #ff3333;">提交失敗</h3>
      <p>${message}</p>
      <button onclick="this.closest('.modal-overlay').remove();"
              style="background-color: #ff3333; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
        確定
      </button>
    </div>
  `;

  document.body.appendChild(errorOverlay);
}
</script>
</body>